import { query } from '@/lib/local-db'

export type ActivityEventType = 'company_added' | 'user_registered' | 'benefit_verified' | 'benefit_disputed'

export interface ActivityLogEntry {
  id: string
  event_type: ActivityEventType
  event_description: string
  user_id?: string
  user_email?: string
  user_name?: string
  company_id?: string
  company_name?: string
  benefit_id?: string
  benefit_name?: string
  metadata?: Record<string, any>
  created_at: string
}

export interface LogActivityParams {
  eventType: ActivityEventType
  description: string
  userId?: string
  userEmail?: string
  userName?: string
  companyId?: string
  companyName?: string
  benefitId?: string
  benefitName?: string
  metadata?: Record<string, any>
}

/**
 * Log an activity to the activity log table
 */
export async function logActivity(params: LogActivityParams): Promise<void> {
  try {
    await query(
      `INSERT INTO activity_log (
        event_type, event_description, user_id, user_email, user_name,
        company_id, company_name, benefit_id, benefit_name, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
      [
        params.eventType,
        params.description,
        params.userId || null,
        params.userEmail || null,
        params.userName || null,
        params.companyId || null,
        params.companyName || null,
        params.benefitId || null,
        params.benefitName || null,
        params.metadata ? JSON.stringify(params.metadata) : null
      ]
    )
    console.log(`✅ Activity logged: ${params.eventType} - ${params.description}`)
  } catch (error) {
    console.error('Error logging activity:', error)
    // Don't throw error to avoid breaking the main operation
  }
}

/**
 * Log a company addition activity
 */
export async function logCompanyAdded(
  companyId: string,
  companyName: string,
  userId?: string,
  userEmail?: string,
  userName?: string
): Promise<void> {
  await logActivity({
    eventType: 'company_added',
    description: `New company added: ${companyName}`,
    userId,
    userEmail,
    userName,
    companyId,
    companyName,
    metadata: {
      source: userId ? 'admin_dashboard' : 'system'
    }
  })
}

/**
 * Log a user registration activity
 */
export async function logUserRegistered(
  userId: string,
  userEmail: string,
  userName?: string,
  companyName?: string
): Promise<void> {
  await logActivity({
    eventType: 'user_registered',
    description: `New user registered: ${userName || userEmail}${companyName ? ` at ${companyName}` : ''}`,
    userId,
    userEmail,
    userName,
    companyName,
    metadata: {
      registration_method: 'magic_link'
    }
  })
}

/**
 * Log a benefit verification activity
 */
export async function logBenefitVerified(
  benefitId: string,
  benefitName: string,
  companyId: string,
  companyName: string,
  userId: string,
  userEmail: string,
  userName?: string
): Promise<void> {
  await logActivity({
    eventType: 'benefit_verified',
    description: `Benefit verified: ${benefitName} at ${companyName} by ${userName || userEmail}`,
    userId,
    userEmail,
    userName,
    companyId,
    companyName,
    benefitId,
    benefitName,
    metadata: {
      verification_type: 'user_confirmation'
    }
  })
}

/**
 * Log a benefit dispute activity
 */
export async function logBenefitDisputed(
  benefitId: string,
  benefitName: string,
  companyId: string,
  companyName: string,
  userId: string,
  userEmail: string,
  userName?: string,
  comment?: string
): Promise<void> {
  await logActivity({
    eventType: 'benefit_disputed',
    description: `Benefit disputed: ${benefitName} at ${companyName} by ${userName || userEmail}`,
    userId,
    userEmail,
    userName,
    companyId,
    companyName,
    benefitId,
    benefitName,
    metadata: {
      dispute_comment: comment,
      verification_type: 'user_dispute'
    }
  })
}

/**
 * Get recent activities for admin dashboard
 */
export async function getRecentActivities(limit: number = 10): Promise<ActivityLogEntry[]> {
  try {
    const result = await query(
      `SELECT 
        id, event_type, event_description, user_id, user_email, user_name,
        company_id, company_name, benefit_id, benefit_name, metadata, created_at
      FROM activity_log 
      ORDER BY created_at DESC 
      LIMIT $1`,
      [limit]
    )
    
    return result.rows.map(row => ({
      ...row,
      metadata: row.metadata ? JSON.parse(row.metadata) : null
    }))
  } catch (error) {
    console.error('Error fetching recent activities:', error)
    return []
  }
}
