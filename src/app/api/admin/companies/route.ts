import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { requireAdmin } from '@/lib/auth'
import { discoverAndNotifyUsers } from '@/lib/user-discovery'
import { sendEmail, createCompanyAddedNotificationEmail } from '@/lib/email'

export async function GET(request: NextRequest) {
  try {
    await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    const search = searchParams.get('search')
    const sort = searchParams.get('sort') || 'created_at' // 'name', 'created_at'
    
    const offset = (page - 1) * limit
    
    let whereClause = ''
    const params: any[] = []
    let paramIndex = 1
    
    const conditions: string[] = []

    if (search) {
      conditions.push(`(name ILIKE $${paramIndex} OR location ILIKE $${paramIndex} OR industry ILIKE $${paramIndex})`)
      params.push(`%${search}%`)
      paramIndex++
    }
    
    if (conditions.length > 0) {
      whereClause = `WHERE ${conditions.join(' AND ')}`
    }
    
    // Get companies with benefit counts and total user counts (via automatic email domain matching)
    const companiesQuery = `
      SELECT
        c.*,
        COUNT(DISTINCT cb.id) as benefit_count,
        COUNT(DISTINCT CASE WHEN cb.is_verified = true THEN cb.id END) as verified_benefit_count,
        COUNT(DISTINCT u.id) as total_user_count
      FROM companies c
      LEFT JOIN company_benefits cb ON c.id = cb.company_id
      LEFT JOIN users u ON c.id = u.company_id
      ${whereClause}
      GROUP BY c.id
      ORDER BY ${sort === 'name' ? 'c.name ASC' : 'c.created_at DESC'}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `
    
    params.push(limit, offset)
    
    const companiesResult = await query(companiesQuery, params)
    
    // Get total count
    const countQuery = `
      SELECT COUNT(DISTINCT c.id) as total
      FROM companies c
      ${whereClause}
    `
    
    const countResult = await query(countQuery, params.slice(0, -2))
    const total = parseInt(countResult.rows[0].total)
    
    return NextResponse.json({
      companies: companiesResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
    
  } catch (error) {
    console.error('Error fetching companies for admin:', error)
    return NextResponse.json(
      { error: 'Failed to fetch companies' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await requireAdmin()

    const body = await request.json()
    const { name, location, industry, size, domain, description, career_url } = body

    if (!name || !location || !industry || !size) {
      return NextResponse.json(
        { error: 'Name, location, industry, and size are required' },
        { status: 400 }
      )
    }

    const result = await query(
      `INSERT INTO companies (name, location, industry, size, domain, description, career_url)
       VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *`,
      [name, location, industry, size, domain || null, description || null, career_url || null]
    )

    const newCompany = result.rows[0]

    // Auto-discover and notify existing users if domain is provided
    // This sends "🎉 Company is now on WorkWell - Verify your benefits!" emails
    let discoveryResult = null
    if (domain) {
      try {
        discoveryResult = await discoverAndNotifyUsers(newCompany.id)
        console.log(`User discovery for ${name}:`, discoveryResult)
      } catch (error) {
        console.error('Error during user discovery:', error)
        // Don't fail company creation if user discovery fails
      }
    }

    // Notify users who reported this company as missing (but exclude those already notified by discovery)
    // This sends "🎉 Company is now available on WorkWell!" emails to users who specifically reported it
    let notificationResult = { notifiedUsers: 0, errors: [] as string[] }
    if (domain) {
      try {
        // Get list of users already notified by discovery to avoid duplicates
        const discoveredEmails = discoveryResult?.discoveredUsers || []

        // Find pending reports for this domain, excluding users already notified by discovery
        let reportsQuery = 'SELECT id, user_email, first_name, last_name FROM missing_company_reports WHERE email_domain = $1 AND status = $2'
        let reportsParams = [domain.toLowerCase(), 'pending']

        if (discoveredEmails.length > 0) {
          const placeholders = discoveredEmails.map((_, index) => `$${index + 3}`).join(', ')
          reportsQuery += ` AND user_email NOT IN (${placeholders})`
          reportsParams.push(...discoveredEmails)
        }

        const reportsResult = await query(reportsQuery, reportsParams)

        for (const report of reportsResult.rows) {
          try {
            // Send notification email (specific to missing company report)
            const emailOptions = createCompanyAddedNotificationEmail(
              report.user_email,
              report.first_name || 'User',
              name,
              newCompany.id
            )
            await sendEmail(emailOptions)

            // Update report status to 'added' and link to company
            await query(
              'UPDATE missing_company_reports SET status = $1, company_id = $2, updated_at = NOW() WHERE id = $3',
              ['added', newCompany.id, report.id]
            )

            notificationResult.notifiedUsers++
            console.log(`✅ Notified user ${report.user_email} about company addition: ${name}`)
          } catch (error) {
            console.error(`Error notifying user ${report.user_email}:`, error)
            notificationResult.errors.push(`Failed to notify ${report.user_email}`)
          }
        }

        // Update status for reports from users who were already notified by discovery
        if (discoveredEmails.length > 0) {
          try {
            const updateDiscoveredQuery = `
              UPDATE missing_company_reports
              SET status = $1, company_id = $2, updated_at = NOW(), admin_notes = $3
              WHERE email_domain = $4 AND status = $5 AND user_email = ANY($6)
            `
            await query(updateDiscoveredQuery, [
              'added',
              newCompany.id,
              'User was notified via company discovery email',
              domain.toLowerCase(),
              'pending',
              discoveredEmails
            ])
            console.log(`✅ Updated ${discoveredEmails.length} missing company reports for users notified via discovery`)
          } catch (error) {
            console.error('Error updating discovered user reports:', error)
          }
        }

        if (notificationResult.notifiedUsers > 0) {
          console.log(`Notified ${notificationResult.notifiedUsers} users who reported missing company: ${name}`)
        }
      } catch (error) {
        console.error('Error processing missing company reports:', error)
        notificationResult.errors.push('Failed to process missing company reports')
      }
    }

    return NextResponse.json({
      ...newCompany,
      userDiscovery: discoveryResult,
      missingCompanyNotifications: notificationResult
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating company:', error)
    return NextResponse.json(
      { error: 'Failed to create company' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    await requireAdmin()
    
    const body = await request.json()
    const { companyId, action, data } = body
    
    if (!companyId || !action) {
      return NextResponse.json(
        { error: 'Company ID and action are required' },
        { status: 400 }
      )
    }
    
    let result
    
    switch (action) {
      case 'update':
        if (!data) {
          return NextResponse.json(
            { error: 'Data is required for update action' },
            { status: 400 }
          )
        }
        
        const keys = Object.keys(data)
        const values = Object.values(data)
        const setClause = keys.map((key, i) => `${key} = $${i + 1}`).join(', ')
        
        result = await query(
          `UPDATE companies SET ${setClause}, updated_at = NOW() WHERE id = $${keys.length + 1} RETURNING *`,
          [...values, companyId]
        )
        break
        
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }
    
    return NextResponse.json(result.rows[0])
    
  } catch (error) {
    console.error('Error updating company:', error)
    return NextResponse.json(
      { error: 'Failed to update company' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')
    
    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }
    
    await query('DELETE FROM companies WHERE id = $1', [companyId])
    
    return NextResponse.json({ success: true })
    
  } catch (error) {
    console.error('Error deleting company:', error)
    return NextResponse.json(
      { error: 'Failed to delete company' },
      { status: 500 }
    )
  }
}
